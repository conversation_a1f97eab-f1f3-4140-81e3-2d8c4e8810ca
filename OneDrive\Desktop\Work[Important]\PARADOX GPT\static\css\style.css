/* Futuristic ParadoxGPT - Dark Glassmorphism Theme */
:root {
    /* Clean Dark Theme - Claude-like */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #3a3a3a;
    --bg-quaternary: #4a4a4a;
    --bg-sidebar: #1e1e1e;
    --bg-input: #2d2d2d;
    --bg-message-user: #2d2d2d;
    --bg-message-assistant: transparent;
    --bg-code: #1e1e1e;
    --bg-modal: rgba(0, 0, 0, 0.8);
    --bg-hover: #333333;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-tertiary: #808080;
    --text-quaternary: #666666;
    --text-inverse: #000000;
    --text-code: #e6e6e6;
    --text-muted: #999999;

    /* Clean Accent Colors */
    --accent-primary: #ff6b35;
    --accent-secondary: #ff6b35;
    --accent-tertiary: #ff6b35;
    --accent-error: #ef4444;
    --accent-success: #22c55e;
    --accent-warning: #f97316;
    --border-color: #404040;

    /* Clean Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 2px 6px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Border Radius */
    --radius-sm: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    --font-display: 'Inter', system-ui, sans-serif;

    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 0px;
    --header-height: 60px;
    --input-max-height: 150px;
    --toggle-button-size: 44px;

    /* Animations */
    --animation-float: float 6s ease-in-out infinite;
    --animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --animation-glow: glow 2s ease-in-out infinite alternate;
}

/* Particle Background Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes glow {
    from { box-shadow: var(--glow-soft); }
    to { box-shadow: var(--glow-primary); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Particle System */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--accent-primary);
    border-radius: 50%;
    animation: var(--animation-float);
    opacity: 0.3;
}

.particle:nth-child(2n) {
    background: var(--accent-secondary);
    animation-delay: -2s;
}

.particle:nth-child(3n) {
    background: var(--accent-tertiary);
    animation-delay: -4s;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    height: 100%;
}

body {
    font-family: var(--font-family);
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow: hidden;
    height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
}



/* App Container */
.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    position: relative;
    z-index: 2;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    width: var(--toggle-button-size);
    height: var(--toggle-button-size);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow-md);
}

.sidebar-toggle:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-hover);
    color: var(--accent-primary);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg), var(--glow-soft);
}

.sidebar-toggle:active {
    transform: translateY(-1px) scale(1.02);
}

/* Hide toggle when sidebar is open */
.sidebar:not(.collapsed) ~ .sidebar-toggle {
    opacity: 0;
    pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 100vw;
        max-width: 320px;
    }

    .sidebar:not(.collapsed) ~ .main-content {
        margin-left: 0;
        width: 100%;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .welcome-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

/* Clean Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-sidebar);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all var(--transition-normal);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    transform: translateX(0);
}

/* Collapsed sidebar state */
.sidebar.collapsed {
    transform: translateX(-100%);
    box-shadow: none;
}



.sidebar-header {
    padding: var(--spacing-lg) var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.new-chat-btn {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-hover);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.new-chat-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--accent-primary);
}

.new-chat-btn:active {
    background: var(--bg-tertiary);
}

.chat-history {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md) 0;
}

.chat-history-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 var(--spacing-lg);
    scrollbar-width: thin;
    scrollbar-color: var(--glass-border) transparent;
}

.chat-history-content::-webkit-scrollbar {
    width: 4px;
}

.chat-history-content::-webkit-scrollbar-track {
    background: transparent;
}

.chat-history-content::-webkit-scrollbar-thumb {
    background: var(--glass-border);
    border-radius: var(--radius-full);
}

.chat-history-content::-webkit-scrollbar-thumb:hover {
    background: var(--glass-border-hover);
}

.empty-history {
    text-align: center;
    padding: var(--spacing-2xl) var(--spacing-lg);
    color: var(--text-tertiary);
    font-size: 0.85rem;
    opacity: 0.7;
}

.sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--glass-border);
    margin-top: auto;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.user-info:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-hover);
    transform: translateY(-1px);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: var(--shadow-sm), var(--glow-soft);
    position: relative;
    overflow: hidden;
}

.user-avatar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

.user-name {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--text-secondary);
    letter-spacing: 0.025em;
}

/* Futuristic Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: transparent;
    position: relative;
    margin-left: 0;
    transition: margin-left var(--transition-normal);
    width: 100%;
}

/* Adjust main content when sidebar is open */
.sidebar:not(.collapsed) ~ .main-content {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
}

.chat-header {
    height: var(--header-height);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--spacing-xl);
    flex-shrink: 0;
    position: relative;
    z-index: 5;
}

.chat-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.02) 50%,
        rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
    z-index: -1;
}



.chat-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 0.025em;
    text-shadow: 0 0 20px var(--accent-primary-glow);
}



/* Futuristic Chat Container */
.chat-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0;
    scroll-behavior: smooth;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: var(--glass-border) transparent;
}

.chat-container::-webkit-scrollbar {
    width: 8px;
}

.chat-container::-webkit-scrollbar-track {
    background: transparent;
}

.chat-container::-webkit-scrollbar-thumb {
    background: var(--glass-border);
    border-radius: var(--radius-full);
    border: 2px solid transparent;
    background-clip: content-box;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: var(--glass-border-hover);
    background-clip: content-box;
}

/* Clean Welcome Section */
.welcome-section {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
    padding: var(--spacing-2xl);
    position: relative;
}

.welcome-content {
    text-align: center;
    max-width: 500px;
}

.welcome-logo {
    margin-bottom: var(--spacing-xl);
    color: var(--accent-primary);
    font-size: 3rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.welcome-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.5;
    font-weight: 400;
}

.welcome-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-3xl);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left var(--transition-slow);
}

.feature-item:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-hover);
    box-shadow: var(--shadow-xl), var(--glow-soft);
    transform: translateY(-4px) scale(1.02);
}

.feature-item:hover::before {
    left: 100%;
}

.feature-item i {
    font-size: 1.5rem;
    color: var(--accent-primary);
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 10px var(--accent-primary-glow));
}

.feature-item span {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    letter-spacing: 0.025em;
}

/* Clean Message Styles */
.message {
    padding: var(--spacing-xl) var(--spacing-lg);
    margin: var(--spacing-md) 0;
    display: flex;
    gap: var(--spacing-md);
    max-width: none;
    position: relative;
    transition: all var(--transition-normal);
}

.message.user {
    background: var(--bg-message-user);
    border-radius: var(--radius-lg);
}

.message.assistant {
    background: var(--bg-message-assistant);
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.message.user .message-avatar {
    background: var(--accent-primary);
    color: white;
}

.message.assistant .message-avatar {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.message-content {
    flex: 1;
    min-width: 0;
    position: relative;
}

.message-content p {
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
    color: var(--text-primary);
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.message-content ul,
.message-content ol {
    margin-bottom: var(--spacing-lg);
    padding-left: var(--spacing-xl);
}

.message-content li {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.message-footer {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    opacity: 0.8;
}

.message-footer small {
    color: var(--text-tertiary);
    font-size: 0.8rem;
    font-weight: 500;
}

.message-actions {
    display: flex;
    gap: var(--spacing-md);
}

.message-action-btn {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    color: var(--text-tertiary);
    font-size: 0.875rem;
    cursor: pointer;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.message-action-btn:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-hover);
    color: var(--accent-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Futuristic Code Blocks */
.message-content pre {
    background: var(--bg-code);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin: var(--spacing-lg) 0;
    position: relative;
    box-shadow: var(--shadow-lg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

.message-content pre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary));
    z-index: 1;
}

.message-content code {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-code);
    font-weight: 500;
}

.message-content p code {
    background: var(--bg-glass);
    color: var(--accent-primary);
    padding: 0.2rem 0.4rem;
    border-radius: var(--radius-sm);
    font-size: 0.85rem;
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

.code-block-container {
    position: relative;
    margin: var(--spacing-lg) 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.code-block-container pre {
    padding: var(--spacing-xl);
    margin: 0;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--glass-border) transparent;
}

.code-block-container pre::-webkit-scrollbar {
    height: 6px;
}

.code-block-container pre::-webkit-scrollbar-track {
    background: transparent;
}

.code-block-container pre::-webkit-scrollbar-thumb {
    background: var(--glass-border);
    border-radius: var(--radius-full);
}

.code-controls {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    display: flex;
    gap: var(--spacing-sm);
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: 10;
}

.code-block-container:hover .code-controls {
    opacity: 1;
}

.code-controls button {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop-strong);
    -webkit-backdrop-filter: var(--glass-backdrop-strong);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-md);
}

.code-controls button:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-hover);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg), var(--glow-soft);
}

.preview-button {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary)) !important;
    color: var(--text-inverse) !important;
    border-color: var(--accent-primary) !important;
    box-shadow: var(--shadow-lg), var(--glow-primary) !important;
}

.preview-button:hover {
    background: linear-gradient(135deg, var(--accent-secondary), var(--accent-primary)) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: var(--shadow-xl), var(--glow-primary) !important;
}

/* Futuristic Input Section */
.input-section {
    flex-shrink: 0;
    background: transparent;
    position: relative;
    z-index: 5;
    padding: var(--spacing-lg);
}



.input-container {
    padding: 0;
    max-width: 700px;
    margin: 0 auto;
}

.chat-form {
    position: relative;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-sm);
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    transition: all var(--transition-normal);
    position: relative;
}

.input-wrapper:focus-within {
    border-color: var(--accent-primary);
}

.message-input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: 1rem;
    line-height: 1.6;
    resize: none;
    outline: none;
    max-height: var(--input-max-height);
    min-height: 24px;
    overflow-y: auto;
    padding: 0;
    margin-right: 0;
    font-weight: 500;
    scrollbar-width: thin;
    scrollbar-color: var(--glass-border) transparent;
}

.message-input::placeholder {
    color: var(--text-tertiary);
    font-weight: 400;
}

.message-input::-webkit-scrollbar {
    width: 6px;
}

.message-input::-webkit-scrollbar-track {
    background: transparent;
}

.message-input::-webkit-scrollbar-thumb {
    background: var(--glass-border);
    border-radius: var(--radius-full);
}

.send-btn {
    background: var(--accent-primary);
    border: none;
    color: white;
    cursor: pointer;
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    width: 36px;
    height: 36px;
    transition: all var(--transition-normal);
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: var(--accent-secondary);
}

.send-btn:disabled {
    background: var(--bg-hover);
    color: var(--text-quaternary);
    cursor: not-allowed;
}

.input-footer {
    margin-top: var(--spacing-md);
    text-align: center;
}

.disclaimer {
    font-size: 0.7rem;
    color: var(--text-quaternary);
    line-height: 1.4;
    font-weight: 400;
    opacity: 0.6;
}

/* Futuristic Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
}

.typing-dots {
    display: flex;
    gap: var(--spacing-sm);
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: var(--radius-full);
    animation: typing 1.6s infinite ease-in-out;
    box-shadow: var(--glow-soft);
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.3s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.6s;
}

/* Futuristic Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-modal);
    backdrop-filter: var(--glass-backdrop-strong);
    -webkit-backdrop-filter: var(--glass-backdrop-strong);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-2xl), var(--glow-primary);
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-xl);
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-radius: var(--radius-full);
    animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
    border-top: 4px solid var(--accent-primary);
    animation-duration: 2s;
}

.spinner-ring:nth-child(2) {
    border-right: 4px solid var(--accent-secondary);
    animation-duration: 1.5s;
    animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
    border-bottom: 4px solid var(--accent-tertiary);
    animation-duration: 1s;
}

.loading-text {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Animations */
@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.6) translateY(0);
        opacity: 0.3;
    }
    40% {
        transform: scale(1.2) translateY(-8px);
        opacity: 1;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Futuristic HTML Preview Modal */
.preview-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-modal);
    backdrop-filter: var(--glass-backdrop-strong);
    -webkit-backdrop-filter: var(--glass-backdrop-strong);
    animation: fadeIn 0.3s ease-out;
}

.preview-modal-content {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    margin: 2% auto;
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    width: 92%;
    height: 92%;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-2xl), var(--glow-primary);
    animation: scaleIn 0.4s ease-out;
    overflow: hidden;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--glass-border);
    background: var(--bg-glass-hover);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    position: relative;
}

.preview-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary));
}

.preview-title {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.preview-title i {
    color: var(--accent-primary);
    filter: drop-shadow(0 0 10px var(--accent-primary-glow));
}

.preview-controls {
    display: flex;
    gap: var(--spacing-md);
}

.preview-btn {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    font-size: 1.1rem;
}

.preview-btn:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-hover);
    color: var(--accent-primary);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg), var(--glow-soft);
}

.preview-body {
    flex: 1;
    overflow: hidden;
    position: relative;
}

#previewFrame {
    width: 100%;
    height: 100%;
    border: none;
    background-color: white;
    border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
}

.preview-modal.fullscreen {
    z-index: 1001;
}

.preview-modal.fullscreen .preview-modal-content {
    width: 100%;
    height: 100%;
    margin: 0;
    border-radius: 0;
}

.preview-modal.fullscreen .preview-header {
    border-radius: 0;
}

.preview-modal.fullscreen .preview-body,
.preview-modal.fullscreen #previewFrame {
    border-radius: 0;
}

/* Futuristic Responsive Design */
@media (max-width: 1200px) {
    :root {
        --sidebar-width: 280px;
        --spacing-3xl: 3rem;
    }

    .welcome-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .feature-item {
        justify-content: center;
    }

    .welcome-title {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    :root {
        --sidebar-width: 100vw;
        --header-height: 60px;
        --spacing-2xl: 2rem;
        --spacing-3xl: 2.5rem;
    }

    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: 100%;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
        box-shadow: var(--shadow-2xl);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .mobile-menu-btn {
        display: flex;
    }

    .chat-header {
        padding: 0 var(--spacing-lg);
        height: var(--header-height);
    }

    .chat-title {
        font-size: 1.1rem;
    }

    .welcome-section {
        padding: var(--spacing-2xl) var(--spacing-lg);
    }

    .welcome-title {
        font-size: 2.5rem;
    }

    .welcome-subtitle {
        font-size: 1.1rem;
    }

    .logo {
        width: 80px;
        height: 80px;
    }

    .logo i {
        font-size: 2rem;
    }

    .message {
        padding: var(--spacing-xl) var(--spacing-lg);
        margin: var(--spacing-md) var(--spacing-lg);
        gap: var(--spacing-lg);
    }

    .message-avatar {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }

    .input-container {
        padding: var(--spacing-lg);
    }

    .preview-modal-content {
        width: 96%;
        height: 96%;
        margin: 2% auto;
        border-radius: var(--radius-xl);
    }

    .code-controls {
        opacity: 1;
        position: static;
        margin-top: var(--spacing-md);
        justify-content: flex-end;
        background: var(--bg-glass);
        backdrop-filter: var(--glass-backdrop);
        -webkit-backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--radius-lg);
        padding: var(--spacing-sm);
    }

    .code-block-container {
        border-radius: var(--radius-lg);
    }
}

@media (max-width: 480px) {
    :root {
        --spacing-xl: 1.25rem;
        --spacing-2xl: 1.75rem;
        --spacing-3xl: 2rem;
    }

    .welcome-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }

    .welcome-features {
        gap: var(--spacing-md);
        grid-template-columns: 1fr;
    }

    .feature-item {
        padding: var(--spacing-lg);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .message {
        padding: var(--spacing-lg) var(--spacing-md);
        margin: var(--spacing-md);
        gap: var(--spacing-md);
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .input-wrapper {
        padding: var(--spacing-md);
    }

    .send-btn {
        width: 40px;
        height: 40px;
    }

    .chat-header {
        padding: 0 var(--spacing-md);
    }

    .sidebar-header,
    .sidebar-footer {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .input-container {
        padding: var(--spacing-md);
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.fade-in {
    animation: fadeIn 0.4s ease-out;
}

.slide-in-left {
    animation: slideInFromLeft 0.4s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

.glow {
    box-shadow: var(--glow-primary);
}

/* Enhanced Focus Styles for Accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px var(--accent-primary-glow);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --glass-border: #ffffff;
        --glass-border-hover: #ffffff;
        --text-tertiary: #ffffff;
        --bg-glass: rgba(255, 255, 255, 0.1);
        --bg-glass-hover: rgba(255, 255, 255, 0.2);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .particles {
        display: none;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .chat-header,
    .input-section,
    .code-controls,
    .message-actions {
        display: none !important;
    }

    .main-content {
        margin: 0;
        padding: 0;
    }

    .message {
        break-inside: avoid;
        margin: 1rem 0;
        padding: 1rem;
        border: 1px solid #ccc;
        border-radius: 0.5rem;
    }
}